{"cells": [{"cell_type": "code", "execution_count": 2, "id": "1ea0b0fb", "metadata": {}, "outputs": [], "source": ["using Flux"]}, {"cell_type": "code", "execution_count": 3, "id": "68c955b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["actual (generic function with 1 method)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# This example will predict the output of the function 4x + 2. \n", "actual(x) = 4x + 2"]}, {"cell_type": "code", "execution_count": 4, "id": "57ec09f2", "metadata": {}, "outputs": [], "source": ["# training and test data\n", "x_train, x_test = hcat(0:5...), hcat(6:10...);\n", "\n", "# training and test labels (true output values)\n", "y_train, y_test = actual.(x_train), actual.(x_test);"]}, {"cell_type": "code", "execution_count": 5, "id": "ad22d458", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x_train = [0 1 2 3 4 5]\n", "y_train = [2 6 10 14 18 22]\n", "size(x_train) = (1, 6)\n"]}, {"data": {"text/plain": ["(1, 6)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@show x_train;\n", "@show y_train;\n", "@show size(x_train)"]}, {"cell_type": "code", "execution_count": 6, "id": "8885a843", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model.weight = Float32[0.6354202;;]\n", "model.bias = Float32[0.0]\n"]}], "source": ["# build the model\n", "model = Dense(1 => 1)   # TODO look at the struct for <PERSON><PERSON>, the constructor and the functor\n", "@show model.weight  # at default initializer values\n", "@show model.bias;   # ditto"]}, {"cell_type": "code", "execution_count": 7, "id": "ee1f7cf6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dense(1 => 1)       \u001b[90m# 2 parameters\u001b[39m"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# a model is a prediction function; training is needed to update the parameters to make more accurate predictions\n", "predict = Dense(1 => 1)"]}, {"cell_type": "code", "execution_count": 8, "id": "dab3f59e", "metadata": {}, "outputs": [{"data": {"text/plain": ["1×6 Matrix{Float32}:\n", " 0.0  -1.22927  -2.45853  -3.6878  -4.91706  -6.14633"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict(x_train)"]}, {"cell_type": "code", "execution_count": null, "id": "27d018ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["306.95715f0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["using Statistics\n", "loss(model, x, y) = mean(abs2.(model(x) .- y));  # this is equivalent to Flux.mse\n", "loss(predict, x_train, y_train)"]}, {"cell_type": "code", "execution_count": 10, "id": "08bac125", "metadata": {}, "outputs": [{"data": {"text/plain": ["Descent(0.1f0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["opt = Descent()"]}, {"cell_type": "code", "execution_count": 11, "id": "e6b6e01a", "metadata": {}, "outputs": [], "source": ["data = [(x_train, y_train)];"]}, {"cell_type": "code", "execution_count": 13, "id": "c2fd5b05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["predict.weight = Float32[-1.2292656;;]\n", "predict.bias = Float32[0.0]\n"]}], "source": ["@show predict.weight\n", "@show predict.bias;"]}, {"cell_type": "code", "execution_count": null, "id": "3b887e1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["276.51956f0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Flux.train!(loss, predict, data, opt)\n", "loss(predict, x_train, y_train)  # after 1 iteration--a bit of improvement"]}, {"cell_type": "code", "execution_count": null, "id": "50ab4fa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.4075283f-9"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for epoch in 1:500   # loss for 200 iters ~ 0.00809; for 500 iters 1.407f-9\n", "    Flux.train!(loss, predict, data, opt)\n", "end\n", "loss(predict, x_train, y_train)"]}, {"cell_type": "code", "execution_count": 22, "id": "53232d77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["predict.weight = Float32[3.9999886;;]\n", "predict.bias = Float32[1.9999969]\n"]}], "source": ["@show predict.weight\n", "@show predict.bias;"]}, {"cell_type": "code", "execution_count": null, "id": "d7b0739f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["predict(x_test) = Float32[25.999928 29.999916 33.999905 37.999893 41.99988]\n", "y_test = [26 30 34 38 42]\n"]}], "source": ["# how did we do on the test data, which was withheld from training?\n", "@show predict(x_test)\n", "@show y_test;\n", "# in this case the training data was perfect--there was no random component \n", "# we get ridiculously close outcomes"]}], "metadata": {"kernelspec": {"display_name": "Julia 1.11.5", "language": "julia", "name": "julia-1.11"}, "language_info": {"file_extension": ".jl", "mimetype": "application/julia", "name": "julia", "version": "1.11.5"}}, "nbformat": 4, "nbformat_minor": 5}