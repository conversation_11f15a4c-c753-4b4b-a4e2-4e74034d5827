{"cells": [{"cell_type": "code", "execution_count": 9, "id": "273e13d0", "metadata": {}, "outputs": [], "source": ["using Flux"]}, {"cell_type": "markdown", "id": "2b1a0391", "metadata": {}, "source": ["In Flux 0.15, almost any parameterised function in Julia is a valid Flux model -- such as this closure over w, b, v. The same function can also be implemented with built-in layers as shown.\n", "\n", "This is a pretty unclear way to do things.  Note here is an alternative model formula, which is much clearer:\n", "```<PERSON>\n", "model = Chain(vcat, <PERSON><PERSON>(1 => 23, tanh), <PERSON><PERSON>(23 => 1, bias=false), only)\n", "```\n", "\n", "Although it's only a tiny bit clearer. "]}, {"cell_type": "code", "execution_count": 10, "id": "481216e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["41-element Vector{Tuple{Float32, Float32}}:\n", " (-2.0, 4.0)\n", " (-1.9, 3.0589998)\n", " (-1.8, 2.2319994)\n", " (-1.7, 1.513)\n", " (-1.6, 0.89600015)\n", " (-1.5, 0.375)\n", " (-1.4, -0.055999994)\n", " (-1.3, -0.40300012)\n", " (-1.2, -0.67199993)\n", " (-1.1, -0.86899996)\n", " ⋮\n", " (1.2, 0.67199993)\n", " (1.3, 0.40300012)\n", " (1.4, 0.055999994)\n", " (1.5, -0.375)\n", " (1.6, -0.89600015)\n", " (1.7, -1.513)\n", " (1.8, -2.2319994)\n", " (1.9, -3.0589998)\n", " (2.0, -4.0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = [(x, 2x-x^3) for x in -2:0.1f0:2] # array of tuples"]}, {"cell_type": "code", "execution_count": 19, "id": "9a28415c", "metadata": {}, "outputs": [{"data": {"text/plain": ["#34 (generic function with 1 method)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = let    # this is old style. Flux now defauts to weight and bias--sometimes?\n", "  w, b, v = (randn(Float32, 23) for _ in 1:3)  # parameters\n", "  x -> sum(v .* tanh.(w*x .+ b))               # callable\n", "end"]}, {"cell_type": "code", "execution_count": 21, "id": "34eaab9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["23-element Vector{Float32}:\n", " -0.1422046\n", " -1.6640896\n", "  0.1547708\n", " -0.74151194\n", "  0.5424113\n", " -0.42498535\n", "  0.12736343\n", "  0.108259335\n", " -0.8743909\n", " -1.916954\n", "  ⋮\n", "  1.9522815\n", " -0.06603946\n", " -0.6359535\n", " -0.9811438\n", "  0.13577957\n", " -1.4811342\n", " -0.621893\n", " -0.32798195\n", " -1.5970912"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.w"]}, {"cell_type": "code", "execution_count": 22, "id": "8e164a56", "metadata": {}, "outputs": [{"data": {"text/plain": ["(:v, :b, :w)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fieldnames(typeof(model))"]}, {"cell_type": "code", "execution_count": 26, "id": "9492c106", "metadata": {}, "outputs": [], "source": ["opt_state = Flux.setup(<PERSON>(), model)\n", "for epoch in 1:1000\n", "  Flux.train!((m,x,y) -> (m(x) - y)^2, model, data, opt_state)\n", "end"]}, {"cell_type": "code", "execution_count": 27, "id": "8f001cfb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\"?>\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"600\" height=\"400\" viewBox=\"0 0 2400 1600\">\n", "<defs>\n", "  <clipPath id=\"clip070\">\n", "    <rect x=\"0\" y=\"0\" width=\"2400\" height=\"1600\"/>\n", "  </clipPath>\n", "</defs>\n", "<path clip-path=\"url(#clip070)\" d=\"M0 1600 L2400 1600 L2400 8.88178e-14 L0 8.88178e-14  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<defs>\n", "  <clipPath id=\"clip071\">\n", "    <rect x=\"480\" y=\"0\" width=\"1681\" height=\"1600\"/>\n", "  </clipPath>\n", "</defs>\n", "<path clip-path=\"url(#clip070)\" d=\"M149.191 1486.45 L2352.76 1486.45 L2352.76 47.2441 L149.191 47.2441  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<defs>\n", "  <clipPath id=\"clip072\">\n", "    <rect x=\"149\" y=\"47\" width=\"2205\" height=\"1440\"/>\n", "  </clipPath>\n", "</defs>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"211.556,1486.45 211.556,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"731.265,1486.45 731.265,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"1250.97,1486.45 1250.97,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"1770.68,1486.45 1770.68,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"2290.39,1486.45 2290.39,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,1445.72 2352.76,1445.72 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,1106.28 2352.76,1106.28 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,766.846 2352.76,766.846 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,427.411 2352.76,427.411 \"/>\n", "<polyline clip-path=\"url(#clip072)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,87.9763 2352.76,87.9763 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1486.45 2352.76,1486.45 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"211.556,1486.45 211.556,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"731.265,1486.45 731.265,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1250.97,1486.45 1250.97,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1770.68,1486.45 1770.68,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"2290.39,1486.45 2290.39,1467.55 \"/>\n", "<path clip-path=\"url(#clip070)\" d=\"M181.498 1532.02 L211.174 1532.02 L211.174 1535.95 L181.498 1535.95 L181.498 1532.02 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M225.294 1544.91 L241.614 1544.91 L241.614 1548.85 L219.669 1548.85 L219.669 1544.91 Q222.331 1542.16 226.915 1537.53 Q231.521 1532.88 232.702 1531.53 Q234.947 1529.01 235.827 1527.27 Q236.729 1525.51 236.729 1523.82 Q236.729 1521.07 234.785 1519.33 Q232.864 1517.6 229.762 1517.6 Q227.563 1517.6 225.109 1518.36 Q222.679 1519.13 219.901 1520.68 L219.901 1515.95 Q222.725 1514.82 225.179 1514.24 Q227.632 1513.66 229.669 1513.66 Q235.04 1513.66 238.234 1516.35 Q241.428 1519.03 241.428 1523.52 Q241.428 1525.65 240.618 1527.57 Q239.831 1529.47 237.725 1532.07 Q237.146 1532.74 234.044 1535.95 Q230.942 1539.15 225.294 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M701.022 1532.02 L730.698 1532.02 L730.698 1535.95 L701.022 1535.95 L701.022 1532.02 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M741.6 1544.91 L749.239 1544.91 L749.239 1518.55 L740.929 1520.21 L740.929 1515.95 L749.193 1514.29 L753.869 1514.29 L753.869 1544.91 L761.508 1544.91 L761.508 1548.85 L741.6 1548.85 L741.6 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M1250.97 1517.37 Q1247.36 1517.37 1245.53 1520.93 Q1243.73 1524.47 1243.73 1531.6 Q1243.73 1538.71 1245.53 1542.27 Q1247.36 1545.82 1250.97 1545.82 Q1254.61 1545.82 1256.41 1542.27 Q1258.24 1538.71 1258.24 1531.6 Q1258.24 1524.47 1256.41 1520.93 Q1254.61 1517.37 1250.97 1517.37 M1250.97 1513.66 Q1256.78 1513.66 1259.84 1518.27 Q1262.92 1522.85 1262.92 1531.6 Q1262.92 1540.33 1259.84 1544.94 Q1256.78 1549.52 1250.97 1549.52 Q1245.16 1549.52 1242.08 1544.94 Q1239.03 1540.33 1239.03 1531.6 Q1239.03 1522.85 1242.08 1518.27 Q1245.16 1513.66 1250.97 1513.66 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M1761.06 1544.91 L1768.7 1544.91 L1768.7 1518.55 L1760.39 1520.21 L1760.39 1515.95 L1768.66 1514.29 L1773.33 1514.29 L1773.33 1544.91 L1780.97 1544.91 L1780.97 1548.85 L1761.06 1548.85 L1761.06 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2285.04 1544.91 L2301.36 1544.91 L2301.36 1548.85 L2279.42 1548.85 L2279.42 1544.91 Q2282.08 1542.16 2286.66 1537.53 Q2291.27 1532.88 2292.45 1531.53 Q2294.7 1529.01 2295.58 1527.27 Q2296.48 1525.51 2296.48 1523.82 Q2296.48 1521.07 2294.53 1519.33 Q2292.61 1517.6 2289.51 1517.6 Q2287.31 1517.6 2284.86 1518.36 Q2282.43 1519.13 2279.65 1520.68 L2279.65 1515.95 Q2282.47 1514.82 2284.93 1514.24 Q2287.38 1513.66 2289.42 1513.66 Q2294.79 1513.66 2297.98 1516.35 Q2301.18 1519.03 2301.18 1523.52 Q2301.18 1525.65 2300.37 1527.57 Q2299.58 1529.47 2297.47 1532.07 Q2296.9 1532.74 2293.79 1535.95 Q2290.69 1539.15 2285.04 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1486.45 149.191,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1445.72 168.089,1445.72 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1106.28 168.089,1106.28 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,766.846 168.089,766.846 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,427.411 168.089,427.411 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,87.9763 168.089,87.9763 \"/>\n", "<path clip-path=\"url(#clip070)\" d=\"M50.9921 1446.17 L80.6679 1446.17 L80.6679 1450.1 L50.9921 1450.1 L50.9921 1446.17 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M103.608 1432.51 L91.8021 1450.96 L103.608 1450.96 L103.608 1432.51 M102.381 1428.44 L108.26 1428.44 L108.26 1450.96 L113.191 1450.96 L113.191 1454.85 L108.26 1454.85 L108.26 1463 L103.608 1463 L103.608 1454.85 L88.0058 1454.85 L88.0058 1450.33 L102.381 1428.44 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M53.0754 1106.73 L82.7512 1106.73 L82.7512 1110.67 L53.0754 1110.67 L53.0754 1106.73 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M96.8715 1119.63 L113.191 1119.63 L113.191 1123.56 L91.2465 1123.56 L91.2465 1119.63 Q93.9086 1116.87 98.4919 1112.24 Q103.098 1107.59 104.279 1106.25 Q106.524 1103.72 107.404 1101.99 Q108.307 1100.23 108.307 1098.54 Q108.307 1095.78 106.362 1094.05 Q104.441 1092.31 101.339 1092.31 Q99.14 1092.31 96.6863 1093.07 Q94.2558 1093.84 91.478 1095.39 L91.478 1090.67 Q94.3021 1089.53 96.7558 1088.95 Q99.2095 1088.38 101.246 1088.38 Q106.617 1088.38 109.811 1091.06 Q113.006 1093.75 113.006 1098.24 Q113.006 1100.37 112.196 1102.29 Q111.408 1104.19 109.302 1106.78 Q108.723 1107.45 105.621 1110.67 Q102.52 1113.86 96.8715 1119.63 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M101.246 752.645 Q97.6354 752.645 95.8067 756.209 Q94.0012 759.751 94.0012 766.881 Q94.0012 773.987 95.8067 777.552 Q97.6354 781.094 101.246 781.094 Q104.881 781.094 106.686 777.552 Q108.515 773.987 108.515 766.881 Q108.515 759.751 106.686 756.209 Q104.881 752.645 101.246 752.645 M101.246 748.941 Q107.057 748.941 110.112 753.547 Q113.191 758.131 113.191 766.881 Q113.191 775.608 110.112 780.214 Q107.057 784.797 101.246 784.797 Q95.4363 784.797 92.3576 780.214 Q89.3021 775.608 89.3021 766.881 Q89.3021 758.131 92.3576 753.547 Q95.4363 748.941 101.246 748.941 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M96.8715 440.756 L113.191 440.756 L113.191 444.691 L91.2465 444.691 L91.2465 440.756 Q93.9086 438.001 98.4919 433.372 Q103.098 428.719 104.279 427.376 Q106.524 424.853 107.404 423.117 Q108.307 421.358 108.307 419.668 Q108.307 416.914 106.362 415.177 Q104.441 413.441 101.339 413.441 Q99.14 413.441 96.6863 414.205 Q94.2558 414.969 91.478 416.52 L91.478 411.798 Q94.3021 410.664 96.7558 410.085 Q99.2095 409.506 101.246 409.506 Q106.617 409.506 109.811 412.191 Q113.006 414.876 113.006 419.367 Q113.006 421.497 112.196 423.418 Q111.408 425.316 109.302 427.909 Q108.723 428.58 105.621 431.798 Q102.52 434.992 96.8715 440.756 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M103.608 74.7703 L91.8021 93.2193 L103.608 93.2193 L103.608 74.7703 M102.381 70.6963 L108.26 70.6963 L108.26 93.2193 L113.191 93.2193 L113.191 97.1082 L108.26 97.1082 L108.26 105.256 L103.608 105.256 L103.608 97.1082 L88.0058 97.1082 L88.0058 92.5943 L102.381 70.6963 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><polyline clip-path=\"url(#clip072)\" style=\"stroke:#009af9; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"211.556,87.9763 216.921,105.389 222.287,122.586 227.652,139.568 233.017,156.336 248.729,204.223 264.441,250.315 280.153,294.64 295.866,337.226 311.578,378.102 327.29,417.295 343.002,454.835 358.714,490.748 373.332,522.726 387.951,553.344 402.57,582.624 417.188,610.588 436.782,646.047 456.377,679.239 475.971,710.218 495.565,739.038 511.756,761.262 527.947,782.081 544.138,801.524 560.329,819.623 575.385,835.275 590.441,849.816 605.497,863.27 620.553,875.663 639.453,889.756 658.353,902.264 677.254,913.236 696.154,922.722 713.108,930.005 730.062,936.168 747.015,941.244 763.969,945.27 783.375,948.633 802.781,950.719 822.187,951.58 841.593,951.271 870.604,948.738 899.616,943.883 936.723,934.57 973.829,922.118 1007.7,908.328 1041.56,892.514 1080.11,872.411 1118.65,850.466 1149.21,832.036 1179.77,812.917 1255.69,763.766 1313.83,726.091 1354.82,700.374 1395.81,675.924 1426.44,658.777 1457.07,642.824 1491.9,626.399 1526.73,612.094 1561.23,600.318 1595.73,591.221 1614.7,587.466 1633.68,584.662 1652.65,582.857 1671.63,582.102 1700.62,583.086 1729.61,586.812 1747.9,590.653 1766.19,595.699 1784.48,601.997 1802.77,609.589 1819.69,617.801 1836.6,627.195 1853.52,637.805 1870.44,649.665 1887.89,663.244 1905.33,678.23 1922.78,694.661 1940.23,712.575 1957.91,732.287 1975.6,753.603 1993.28,776.562 2010.97,801.206 2029.43,828.779 2047.9,858.278 2066.36,889.748 2084.83,923.236 2099.38,951.066 2113.92,980.198 2128.47,1010.65 2143.02,1042.46 2159.58,1080.34 2176.14,1120.03 2192.71,1161.56 2209.27,1204.97 2225.83,1250.28 2242.4,1297.54 2258.96,1346.76 2275.52,1398 2279.24,1409.77 2282.96,1421.65 2286.67,1433.63 2290.39,1445.72 \"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"211.556\" cy=\"207.293\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"263.527\" cy=\"324.632\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"315.498\" cy=\"440.876\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"367.469\" cy=\"553.586\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"419.439\" cy=\"660.047\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"471.41\" cy=\"757.197\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"523.381\" cy=\"841.604\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"575.352\" cy=\"909.692\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"627.323\" cy=\"958.579\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"679.294\" cy=\"987.619\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"731.265\" cy=\"999.802\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"783.236\" cy=\"1001.18\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"835.206\" cy=\"997.74\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"887.177\" cy=\"992.151\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"939.148\" cy=\"983.269\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"991.119\" cy=\"968.318\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1043.09\" cy=\"945.436\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1095.06\" cy=\"914.814\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1147.03\" cy=\"878.391\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1199\" cy=\"839.142\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1250.97\" cy=\"800.333\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1302.94\" cy=\"764.497\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1354.92\" cy=\"732.597\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1406.89\" cy=\"704.197\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1458.86\" cy=\"678.42\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1510.83\" cy=\"654.825\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1562.8\" cy=\"633.776\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1614.77\" cy=\"616.412\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1666.74\" cy=\"604.482\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1718.71\" cy=\"600.201\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1770.68\" cy=\"606.051\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1822.65\" cy=\"624.477\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1874.62\" cy=\"657.481\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1926.59\" cy=\"706.253\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"1978.57\" cy=\"770.97\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2030.54\" cy=\"850.784\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2082.51\" cy=\"943.95\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2134.48\" cy=\"1048\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2186.45\" cy=\"1159.94\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2238.42\" cy=\"1276.51\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip072)\" cx=\"2290.39\" cy=\"1394.44\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<path clip-path=\"url(#clip070)\" d=\"M1888.37 250.738 L2279.3 250.738 L2279.3 95.2176 L1888.37 95.2176  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1888.37,250.738 2279.3,250.738 2279.3,95.2176 1888.37,95.2176 1888.37,250.738 \"/>\n", "<polyline clip-path=\"url(#clip070)\" style=\"stroke:#009af9; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1912.85,147.058 2059.76,147.058 \"/>\n", "<path clip-path=\"url(#clip070)\" d=\"M2091.65 131.051 L2091.65 138.412 L2100.42 138.412 L2100.42 141.722 L2091.65 141.722 L2091.65 155.796 Q2091.65 158.967 2092.51 159.87 Q2093.39 160.773 2096.05 160.773 L2100.42 160.773 L2100.42 164.338 L2096.05 164.338 Q2091.12 164.338 2089.24 162.509 Q2087.37 160.657 2087.37 155.796 L2087.37 141.722 L2084.24 141.722 L2084.24 138.412 L2087.37 138.412 L2087.37 131.051 L2091.65 131.051 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2121.05 142.393 Q2120.33 141.977 2119.47 141.791 Q2118.64 141.583 2117.62 141.583 Q2114.01 141.583 2112.07 143.944 Q2110.14 146.282 2110.14 150.68 L2110.14 164.338 L2105.86 164.338 L2105.86 138.412 L2110.14 138.412 L2110.14 142.44 Q2111.49 140.078 2113.64 138.944 Q2115.79 137.787 2118.87 137.787 Q2119.31 137.787 2119.84 137.856 Q2120.38 137.903 2121.02 138.018 L2121.05 142.393 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2125.08 154.106 L2125.08 138.412 L2129.33 138.412 L2129.33 153.944 Q2129.33 157.625 2130.77 159.476 Q2132.2 161.305 2135.07 161.305 Q2138.52 161.305 2140.51 159.106 Q2142.53 156.907 2142.53 153.111 L2142.53 138.412 L2146.79 138.412 L2146.79 164.338 L2142.53 164.338 L2142.53 160.356 Q2140.98 162.717 2138.92 163.875 Q2136.88 165.009 2134.17 165.009 Q2129.7 165.009 2127.39 162.231 Q2125.08 159.453 2125.08 154.106 M2135.79 137.787 L2135.79 137.787 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2159.77 131.051 L2159.77 138.412 L2168.55 138.412 L2168.55 141.722 L2159.77 141.722 L2159.77 155.796 Q2159.77 158.967 2160.63 159.87 Q2161.51 160.773 2164.17 160.773 L2168.55 160.773 L2168.55 164.338 L2164.17 164.338 Q2159.24 164.338 2157.37 162.509 Q2155.49 160.657 2155.49 155.796 L2155.49 141.722 L2152.37 141.722 L2152.37 138.412 L2155.49 138.412 L2155.49 131.051 L2159.77 131.051 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2195.7 148.689 L2195.7 164.338 L2191.44 164.338 L2191.44 148.828 Q2191.44 145.148 2190.01 143.319 Q2188.57 141.49 2185.7 141.49 Q2182.25 141.49 2180.26 143.69 Q2178.27 145.889 2178.27 149.685 L2178.27 164.338 L2173.99 164.338 L2173.99 128.319 L2178.27 128.319 L2178.27 142.44 Q2179.8 140.102 2181.86 138.944 Q2183.94 137.787 2186.65 137.787 Q2191.12 137.787 2193.41 140.565 Q2195.7 143.319 2195.7 148.689 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><circle clip-path=\"url(#clip070)\" cx=\"1986.31\" cy=\"198.898\" r=\"20.48\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"4.55111\"/>\n", "<path clip-path=\"url(#clip070)\" d=\"M2084.24 180.159 L2088.5 180.159 L2088.5 216.178 L2084.24 216.178 L2084.24 180.159 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2119.59 202.15 L2119.59 204.233 L2100.01 204.233 Q2100.28 208.631 2102.64 210.946 Q2105.03 213.238 2109.26 213.238 Q2111.72 213.238 2114.01 212.636 Q2116.33 212.034 2118.59 210.83 L2118.59 214.858 Q2116.3 215.83 2113.89 216.34 Q2111.49 216.849 2109.01 216.849 Q2102.81 216.849 2099.17 213.238 Q2095.56 209.627 2095.56 203.469 Q2095.56 197.104 2098.99 193.377 Q2102.44 189.627 2108.27 189.627 Q2113.5 189.627 2116.53 193.006 Q2119.59 196.363 2119.59 202.15 M2115.33 200.9 Q2115.28 197.405 2113.36 195.321 Q2111.46 193.238 2108.32 193.238 Q2104.75 193.238 2102.6 195.252 Q2100.47 197.266 2100.14 200.923 L2115.33 200.9 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2138.36 203.145 Q2133.2 203.145 2131.21 204.326 Q2129.22 205.506 2129.22 208.354 Q2129.22 210.622 2130.7 211.965 Q2132.2 213.284 2134.77 213.284 Q2138.32 213.284 2140.45 210.784 Q2142.6 208.261 2142.6 204.094 L2142.6 203.145 L2138.36 203.145 M2146.86 201.386 L2146.86 216.178 L2142.6 216.178 L2142.6 212.242 Q2141.14 214.603 2138.96 215.738 Q2136.79 216.849 2133.64 216.849 Q2129.66 216.849 2127.3 214.627 Q2124.96 212.381 2124.96 208.631 Q2124.96 204.256 2127.88 202.034 Q2130.82 199.812 2136.63 199.812 L2142.6 199.812 L2142.6 199.395 Q2142.6 196.455 2140.65 194.858 Q2138.73 193.238 2135.24 193.238 Q2133.01 193.238 2130.91 193.77 Q2128.8 194.303 2126.86 195.367 L2126.86 191.432 Q2129.2 190.53 2131.39 190.09 Q2133.59 189.627 2135.68 189.627 Q2141.3 189.627 2144.08 192.543 Q2146.86 195.46 2146.86 201.386 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2170.65 194.233 Q2169.94 193.817 2169.08 193.631 Q2168.25 193.423 2167.23 193.423 Q2163.62 193.423 2161.67 195.784 Q2159.75 198.122 2159.75 202.52 L2159.75 216.178 L2155.47 216.178 L2155.47 190.252 L2159.75 190.252 L2159.75 194.28 Q2161.09 191.918 2163.25 190.784 Q2165.4 189.627 2168.48 189.627 Q2168.92 189.627 2169.45 189.696 Q2169.98 189.743 2170.63 189.858 L2170.65 194.233 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2195.84 200.529 L2195.84 216.178 L2191.58 216.178 L2191.58 200.668 Q2191.58 196.988 2190.14 195.159 Q2188.71 193.33 2185.84 193.33 Q2182.39 193.33 2180.4 195.53 Q2178.41 197.729 2178.41 201.525 L2178.41 216.178 L2174.13 216.178 L2174.13 190.252 L2178.41 190.252 L2178.41 194.28 Q2179.94 191.942 2182 190.784 Q2184.08 189.627 2186.79 189.627 Q2191.26 189.627 2193.55 192.405 Q2195.84 195.159 2195.84 200.529 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2226.51 202.15 L2226.51 204.233 L2206.93 204.233 Q2207.2 208.631 2209.57 210.946 Q2211.95 213.238 2216.19 213.238 Q2218.64 213.238 2220.93 212.636 Q2223.25 212.034 2225.51 210.83 L2225.51 214.858 Q2223.22 215.83 2220.82 216.34 Q2218.41 216.849 2215.93 216.849 Q2209.73 216.849 2206.09 213.238 Q2202.48 209.627 2202.48 203.469 Q2202.48 197.104 2205.91 193.377 Q2209.36 189.627 2215.19 189.627 Q2220.42 189.627 2223.45 193.006 Q2226.51 196.363 2226.51 202.15 M2222.25 200.9 Q2222.2 197.405 2220.28 195.321 Q2218.38 193.238 2215.24 193.238 Q2211.67 193.238 2209.52 195.252 Q2207.39 197.266 2207.07 200.923 L2222.25 200.9 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip070)\" d=\"M2250.56 194.187 L2250.56 180.159 L2254.82 180.159 L2254.82 216.178 L2250.56 216.178 L2250.56 212.289 Q2249.22 214.603 2247.16 215.738 Q2245.12 216.849 2242.25 216.849 Q2237.55 216.849 2234.59 213.099 Q2231.65 209.349 2231.65 203.238 Q2231.65 197.127 2234.59 193.377 Q2237.55 189.627 2242.25 189.627 Q2245.12 189.627 2247.16 190.761 Q2249.22 191.872 2250.56 194.187 M2236.05 203.238 Q2236.05 207.937 2237.97 210.622 Q2239.91 213.284 2243.29 213.284 Q2246.67 213.284 2248.62 210.622 Q2250.56 207.937 2250.56 203.238 Q2250.56 198.539 2248.62 195.877 Q2246.67 193.192 2243.29 193.192 Q2239.91 193.192 2237.97 195.877 Q2236.05 198.539 2236.05 203.238 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /></svg>\n"], "text/html": ["<?xml version=\"1.0\" encoding=\"utf-8\"?>\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"600\" height=\"400\" viewBox=\"0 0 2400 1600\">\n", "<defs>\n", "  <clipPath id=\"clip120\">\n", "    <rect x=\"0\" y=\"0\" width=\"2400\" height=\"1600\"/>\n", "  </clipPath>\n", "</defs>\n", "<path clip-path=\"url(#clip120)\" d=\"M0 1600 L2400 1600 L2400 8.88178e-14 L0 8.88178e-14  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<defs>\n", "  <clipPath id=\"clip121\">\n", "    <rect x=\"480\" y=\"0\" width=\"1681\" height=\"1600\"/>\n", "  </clipPath>\n", "</defs>\n", "<path clip-path=\"url(#clip120)\" d=\"M149.191 1486.45 L2352.76 1486.45 L2352.76 47.2441 L149.191 47.2441  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<defs>\n", "  <clipPath id=\"clip122\">\n", "    <rect x=\"149\" y=\"47\" width=\"2205\" height=\"1440\"/>\n", "  </clipPath>\n", "</defs>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"211.556,1486.45 211.556,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"731.265,1486.45 731.265,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"1250.97,1486.45 1250.97,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"1770.68,1486.45 1770.68,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"2290.39,1486.45 2290.39,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,1445.72 2352.76,1445.72 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,1106.28 2352.76,1106.28 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,766.846 2352.76,766.846 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,427.411 2352.76,427.411 \"/>\n", "<polyline clip-path=\"url(#clip122)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:2; stroke-opacity:0.1; fill:none\" points=\"149.191,87.9763 2352.76,87.9763 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1486.45 2352.76,1486.45 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"211.556,1486.45 211.556,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"731.265,1486.45 731.265,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1250.97,1486.45 1250.97,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1770.68,1486.45 1770.68,1467.55 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"2290.39,1486.45 2290.39,1467.55 \"/>\n", "<path clip-path=\"url(#clip120)\" d=\"M181.498 1532.02 L211.174 1532.02 L211.174 1535.95 L181.498 1535.95 L181.498 1532.02 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M225.294 1544.91 L241.614 1544.91 L241.614 1548.85 L219.669 1548.85 L219.669 1544.91 Q222.331 1542.16 226.915 1537.53 Q231.521 1532.88 232.702 1531.53 Q234.947 1529.01 235.827 1527.27 Q236.729 1525.51 236.729 1523.82 Q236.729 1521.07 234.785 1519.33 Q232.864 1517.6 229.762 1517.6 Q227.563 1517.6 225.109 1518.36 Q222.679 1519.13 219.901 1520.68 L219.901 1515.95 Q222.725 1514.82 225.179 1514.24 Q227.632 1513.66 229.669 1513.66 Q235.04 1513.66 238.234 1516.35 Q241.428 1519.03 241.428 1523.52 Q241.428 1525.65 240.618 1527.57 Q239.831 1529.47 237.725 1532.07 Q237.146 1532.74 234.044 1535.95 Q230.942 1539.15 225.294 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M701.022 1532.02 L730.698 1532.02 L730.698 1535.95 L701.022 1535.95 L701.022 1532.02 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M741.6 1544.91 L749.239 1544.91 L749.239 1518.55 L740.929 1520.21 L740.929 1515.95 L749.193 1514.29 L753.869 1514.29 L753.869 1544.91 L761.508 1544.91 L761.508 1548.85 L741.6 1548.85 L741.6 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M1250.97 1517.37 Q1247.36 1517.37 1245.53 1520.93 Q1243.73 1524.47 1243.73 1531.6 Q1243.73 1538.71 1245.53 1542.27 Q1247.36 1545.82 1250.97 1545.82 Q1254.61 1545.82 1256.41 1542.27 Q1258.24 1538.71 1258.24 1531.6 Q1258.24 1524.47 1256.41 1520.93 Q1254.61 1517.37 1250.97 1517.37 M1250.97 1513.66 Q1256.78 1513.66 1259.84 1518.27 Q1262.92 1522.85 1262.92 1531.6 Q1262.92 1540.33 1259.84 1544.94 Q1256.78 1549.52 1250.97 1549.52 Q1245.16 1549.52 1242.08 1544.94 Q1239.03 1540.33 1239.03 1531.6 Q1239.03 1522.85 1242.08 1518.27 Q1245.16 1513.66 1250.97 1513.66 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M1761.06 1544.91 L1768.7 1544.91 L1768.7 1518.55 L1760.39 1520.21 L1760.39 1515.95 L1768.66 1514.29 L1773.33 1514.29 L1773.33 1544.91 L1780.97 1544.91 L1780.97 1548.85 L1761.06 1548.85 L1761.06 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2285.04 1544.91 L2301.36 1544.91 L2301.36 1548.85 L2279.42 1548.85 L2279.42 1544.91 Q2282.08 1542.16 2286.66 1537.53 Q2291.27 1532.88 2292.45 1531.53 Q2294.7 1529.01 2295.58 1527.27 Q2296.48 1525.51 2296.48 1523.82 Q2296.48 1521.07 2294.53 1519.33 Q2292.61 1517.6 2289.51 1517.6 Q2287.31 1517.6 2284.86 1518.36 Q2282.43 1519.13 2279.65 1520.68 L2279.65 1515.95 Q2282.47 1514.82 2284.93 1514.24 Q2287.38 1513.66 2289.42 1513.66 Q2294.79 1513.66 2297.98 1516.35 Q2301.18 1519.03 2301.18 1523.52 Q2301.18 1525.65 2300.37 1527.57 Q2299.58 1529.47 2297.47 1532.07 Q2296.9 1532.74 2293.79 1535.95 Q2290.69 1539.15 2285.04 1544.91 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1486.45 149.191,47.2441 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1445.72 168.089,1445.72 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,1106.28 168.089,1106.28 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,766.846 168.089,766.846 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,427.411 168.089,427.411 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"149.191,87.9763 168.089,87.9763 \"/>\n", "<path clip-path=\"url(#clip120)\" d=\"M50.9921 1446.17 L80.6679 1446.17 L80.6679 1450.1 L50.9921 1450.1 L50.9921 1446.17 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M103.608 1432.51 L91.8021 1450.96 L103.608 1450.96 L103.608 1432.51 M102.381 1428.44 L108.26 1428.44 L108.26 1450.96 L113.191 1450.96 L113.191 1454.85 L108.26 1454.85 L108.26 1463 L103.608 1463 L103.608 1454.85 L88.0058 1454.85 L88.0058 1450.33 L102.381 1428.44 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M53.0754 1106.73 L82.7512 1106.73 L82.7512 1110.67 L53.0754 1110.67 L53.0754 1106.73 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M96.8715 1119.63 L113.191 1119.63 L113.191 1123.56 L91.2465 1123.56 L91.2465 1119.63 Q93.9086 1116.87 98.4919 1112.24 Q103.098 1107.59 104.279 1106.25 Q106.524 1103.72 107.404 1101.99 Q108.307 1100.23 108.307 1098.54 Q108.307 1095.78 106.362 1094.05 Q104.441 1092.31 101.339 1092.31 Q99.14 1092.31 96.6863 1093.07 Q94.2558 1093.84 91.478 1095.39 L91.478 1090.67 Q94.3021 1089.53 96.7558 1088.95 Q99.2095 1088.38 101.246 1088.38 Q106.617 1088.38 109.811 1091.06 Q113.006 1093.75 113.006 1098.24 Q113.006 1100.37 112.196 1102.29 Q111.408 1104.19 109.302 1106.78 Q108.723 1107.45 105.621 1110.67 Q102.52 1113.86 96.8715 1119.63 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M101.246 752.645 Q97.6354 752.645 95.8067 756.209 Q94.0012 759.751 94.0012 766.881 Q94.0012 773.987 95.8067 777.552 Q97.6354 781.094 101.246 781.094 Q104.881 781.094 106.686 777.552 Q108.515 773.987 108.515 766.881 Q108.515 759.751 106.686 756.209 Q104.881 752.645 101.246 752.645 M101.246 748.941 Q107.057 748.941 110.112 753.547 Q113.191 758.131 113.191 766.881 Q113.191 775.608 110.112 780.214 Q107.057 784.797 101.246 784.797 Q95.4363 784.797 92.3576 780.214 Q89.3021 775.608 89.3021 766.881 Q89.3021 758.131 92.3576 753.547 Q95.4363 748.941 101.246 748.941 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M96.8715 440.756 L113.191 440.756 L113.191 444.691 L91.2465 444.691 L91.2465 440.756 Q93.9086 438.001 98.4919 433.372 Q103.098 428.719 104.279 427.376 Q106.524 424.853 107.404 423.117 Q108.307 421.358 108.307 419.668 Q108.307 416.914 106.362 415.177 Q104.441 413.441 101.339 413.441 Q99.14 413.441 96.6863 414.205 Q94.2558 414.969 91.478 416.52 L91.478 411.798 Q94.3021 410.664 96.7558 410.085 Q99.2095 409.506 101.246 409.506 Q106.617 409.506 109.811 412.191 Q113.006 414.876 113.006 419.367 Q113.006 421.497 112.196 423.418 Q111.408 425.316 109.302 427.909 Q108.723 428.58 105.621 431.798 Q102.52 434.992 96.8715 440.756 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M103.608 74.7703 L91.8021 93.2193 L103.608 93.2193 L103.608 74.7703 M102.381 70.6963 L108.26 70.6963 L108.26 93.2193 L113.191 93.2193 L113.191 97.1082 L108.26 97.1082 L108.26 105.256 L103.608 105.256 L103.608 97.1082 L88.0058 97.1082 L88.0058 92.5943 L102.381 70.6963 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><polyline clip-path=\"url(#clip122)\" style=\"stroke:#009af9; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"211.556,87.9763 216.921,105.389 222.287,122.586 227.652,139.568 233.017,156.336 248.729,204.223 264.441,250.315 280.153,294.64 295.866,337.226 311.578,378.102 327.29,417.295 343.002,454.835 358.714,490.748 373.332,522.726 387.951,553.344 402.57,582.624 417.188,610.588 436.782,646.047 456.377,679.239 475.971,710.218 495.565,739.038 511.756,761.262 527.947,782.081 544.138,801.524 560.329,819.623 575.385,835.275 590.441,849.816 605.497,863.27 620.553,875.663 639.453,889.756 658.353,902.264 677.254,913.236 696.154,922.722 713.108,930.005 730.062,936.168 747.015,941.244 763.969,945.27 783.375,948.633 802.781,950.719 822.187,951.58 841.593,951.271 870.604,948.738 899.616,943.883 936.723,934.57 973.829,922.118 1007.7,908.328 1041.56,892.514 1080.11,872.411 1118.65,850.466 1149.21,832.036 1179.77,812.917 1255.69,763.766 1313.83,726.091 1354.82,700.374 1395.81,675.924 1426.44,658.777 1457.07,642.824 1491.9,626.399 1526.73,612.094 1561.23,600.318 1595.73,591.221 1614.7,587.466 1633.68,584.662 1652.65,582.857 1671.63,582.102 1700.62,583.086 1729.61,586.812 1747.9,590.653 1766.19,595.699 1784.48,601.997 1802.77,609.589 1819.69,617.801 1836.6,627.195 1853.52,637.805 1870.44,649.665 1887.89,663.244 1905.33,678.23 1922.78,694.661 1940.23,712.575 1957.91,732.287 1975.6,753.603 1993.28,776.562 2010.97,801.206 2029.43,828.779 2047.9,858.278 2066.36,889.748 2084.83,923.236 2099.38,951.066 2113.92,980.198 2128.47,1010.65 2143.02,1042.46 2159.58,1080.34 2176.14,1120.03 2192.71,1161.56 2209.27,1204.97 2225.83,1250.28 2242.4,1297.54 2258.96,1346.76 2275.52,1398 2279.24,1409.77 2282.96,1421.65 2286.67,1433.63 2290.39,1445.72 \"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"211.556\" cy=\"207.293\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"263.527\" cy=\"324.632\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"315.498\" cy=\"440.876\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"367.469\" cy=\"553.586\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"419.439\" cy=\"660.047\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"471.41\" cy=\"757.197\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"523.381\" cy=\"841.604\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"575.352\" cy=\"909.692\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"627.323\" cy=\"958.579\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"679.294\" cy=\"987.619\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"731.265\" cy=\"999.802\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"783.236\" cy=\"1001.18\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"835.206\" cy=\"997.74\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"887.177\" cy=\"992.151\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"939.148\" cy=\"983.269\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"991.119\" cy=\"968.318\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1043.09\" cy=\"945.436\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1095.06\" cy=\"914.814\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1147.03\" cy=\"878.391\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1199\" cy=\"839.142\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1250.97\" cy=\"800.333\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1302.94\" cy=\"764.497\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1354.92\" cy=\"732.597\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1406.89\" cy=\"704.197\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1458.86\" cy=\"678.42\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1510.83\" cy=\"654.825\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1562.8\" cy=\"633.776\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1614.77\" cy=\"616.412\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1666.74\" cy=\"604.482\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1718.71\" cy=\"600.201\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1770.68\" cy=\"606.051\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1822.65\" cy=\"624.477\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1874.62\" cy=\"657.481\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1926.59\" cy=\"706.253\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"1978.57\" cy=\"770.97\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2030.54\" cy=\"850.784\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2082.51\" cy=\"943.95\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2134.48\" cy=\"1048\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2186.45\" cy=\"1159.94\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2238.42\" cy=\"1276.51\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<circle clip-path=\"url(#clip122)\" cx=\"2290.39\" cy=\"1394.44\" r=\"14.4\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"3.2\"/>\n", "<path clip-path=\"url(#clip120)\" d=\"M1888.37 250.738 L2279.3 250.738 L2279.3 95.2176 L1888.37 95.2176  Z\" fill=\"#ffffff\" fill-rule=\"evenodd\" fill-opacity=\"1\"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#000000; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1888.37,250.738 2279.3,250.738 2279.3,95.2176 1888.37,95.2176 1888.37,250.738 \"/>\n", "<polyline clip-path=\"url(#clip120)\" style=\"stroke:#009af9; stroke-linecap:round; stroke-linejoin:round; stroke-width:4; stroke-opacity:1; fill:none\" points=\"1912.85,147.058 2059.76,147.058 \"/>\n", "<path clip-path=\"url(#clip120)\" d=\"M2091.65 131.051 L2091.65 138.412 L2100.42 138.412 L2100.42 141.722 L2091.65 141.722 L2091.65 155.796 Q2091.65 158.967 2092.51 159.87 Q2093.39 160.773 2096.05 160.773 L2100.42 160.773 L2100.42 164.338 L2096.05 164.338 Q2091.12 164.338 2089.24 162.509 Q2087.37 160.657 2087.37 155.796 L2087.37 141.722 L2084.24 141.722 L2084.24 138.412 L2087.37 138.412 L2087.37 131.051 L2091.65 131.051 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2121.05 142.393 Q2120.33 141.977 2119.47 141.791 Q2118.64 141.583 2117.62 141.583 Q2114.01 141.583 2112.07 143.944 Q2110.14 146.282 2110.14 150.68 L2110.14 164.338 L2105.86 164.338 L2105.86 138.412 L2110.14 138.412 L2110.14 142.44 Q2111.49 140.078 2113.64 138.944 Q2115.79 137.787 2118.87 137.787 Q2119.31 137.787 2119.84 137.856 Q2120.38 137.903 2121.02 138.018 L2121.05 142.393 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2125.08 154.106 L2125.08 138.412 L2129.33 138.412 L2129.33 153.944 Q2129.33 157.625 2130.77 159.476 Q2132.2 161.305 2135.07 161.305 Q2138.52 161.305 2140.51 159.106 Q2142.53 156.907 2142.53 153.111 L2142.53 138.412 L2146.79 138.412 L2146.79 164.338 L2142.53 164.338 L2142.53 160.356 Q2140.98 162.717 2138.92 163.875 Q2136.88 165.009 2134.17 165.009 Q2129.7 165.009 2127.39 162.231 Q2125.08 159.453 2125.08 154.106 M2135.79 137.787 L2135.79 137.787 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2159.77 131.051 L2159.77 138.412 L2168.55 138.412 L2168.55 141.722 L2159.77 141.722 L2159.77 155.796 Q2159.77 158.967 2160.63 159.87 Q2161.51 160.773 2164.17 160.773 L2168.55 160.773 L2168.55 164.338 L2164.17 164.338 Q2159.24 164.338 2157.37 162.509 Q2155.49 160.657 2155.49 155.796 L2155.49 141.722 L2152.37 141.722 L2152.37 138.412 L2155.49 138.412 L2155.49 131.051 L2159.77 131.051 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2195.7 148.689 L2195.7 164.338 L2191.44 164.338 L2191.44 148.828 Q2191.44 145.148 2190.01 143.319 Q2188.57 141.49 2185.7 141.49 Q2182.25 141.49 2180.26 143.69 Q2178.27 145.889 2178.27 149.685 L2178.27 164.338 L2173.99 164.338 L2173.99 128.319 L2178.27 128.319 L2178.27 142.44 Q2179.8 140.102 2181.86 138.944 Q2183.94 137.787 2186.65 137.787 Q2191.12 137.787 2193.41 140.565 Q2195.7 143.319 2195.7 148.689 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><circle clip-path=\"url(#clip120)\" cx=\"1986.31\" cy=\"198.898\" r=\"20.48\" fill=\"#e26f46\" fill-rule=\"evenodd\" fill-opacity=\"1\" stroke=\"#000000\" stroke-opacity=\"1\" stroke-width=\"4.55111\"/>\n", "<path clip-path=\"url(#clip120)\" d=\"M2084.24 180.159 L2088.5 180.159 L2088.5 216.178 L2084.24 216.178 L2084.24 180.159 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2119.59 202.15 L2119.59 204.233 L2100.01 204.233 Q2100.28 208.631 2102.64 210.946 Q2105.03 213.238 2109.26 213.238 Q2111.72 213.238 2114.01 212.636 Q2116.33 212.034 2118.59 210.83 L2118.59 214.858 Q2116.3 215.83 2113.89 216.34 Q2111.49 216.849 2109.01 216.849 Q2102.81 216.849 2099.17 213.238 Q2095.56 209.627 2095.56 203.469 Q2095.56 197.104 2098.99 193.377 Q2102.44 189.627 2108.27 189.627 Q2113.5 189.627 2116.53 193.006 Q2119.59 196.363 2119.59 202.15 M2115.33 200.9 Q2115.28 197.405 2113.36 195.321 Q2111.46 193.238 2108.32 193.238 Q2104.75 193.238 2102.6 195.252 Q2100.47 197.266 2100.14 200.923 L2115.33 200.9 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2138.36 203.145 Q2133.2 203.145 2131.21 204.326 Q2129.22 205.506 2129.22 208.354 Q2129.22 210.622 2130.7 211.965 Q2132.2 213.284 2134.77 213.284 Q2138.32 213.284 2140.45 210.784 Q2142.6 208.261 2142.6 204.094 L2142.6 203.145 L2138.36 203.145 M2146.86 201.386 L2146.86 216.178 L2142.6 216.178 L2142.6 212.242 Q2141.14 214.603 2138.96 215.738 Q2136.79 216.849 2133.64 216.849 Q2129.66 216.849 2127.3 214.627 Q2124.96 212.381 2124.96 208.631 Q2124.96 204.256 2127.88 202.034 Q2130.82 199.812 2136.63 199.812 L2142.6 199.812 L2142.6 199.395 Q2142.6 196.455 2140.65 194.858 Q2138.73 193.238 2135.24 193.238 Q2133.01 193.238 2130.91 193.77 Q2128.8 194.303 2126.86 195.367 L2126.86 191.432 Q2129.2 190.53 2131.39 190.09 Q2133.59 189.627 2135.68 189.627 Q2141.3 189.627 2144.08 192.543 Q2146.86 195.46 2146.86 201.386 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2170.65 194.233 Q2169.94 193.817 2169.08 193.631 Q2168.25 193.423 2167.23 193.423 Q2163.62 193.423 2161.67 195.784 Q2159.75 198.122 2159.75 202.52 L2159.75 216.178 L2155.47 216.178 L2155.47 190.252 L2159.75 190.252 L2159.75 194.28 Q2161.09 191.918 2163.25 190.784 Q2165.4 189.627 2168.48 189.627 Q2168.92 189.627 2169.45 189.696 Q2169.98 189.743 2170.63 189.858 L2170.65 194.233 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2195.84 200.529 L2195.84 216.178 L2191.58 216.178 L2191.58 200.668 Q2191.58 196.988 2190.14 195.159 Q2188.71 193.33 2185.84 193.33 Q2182.39 193.33 2180.4 195.53 Q2178.41 197.729 2178.41 201.525 L2178.41 216.178 L2174.13 216.178 L2174.13 190.252 L2178.41 190.252 L2178.41 194.28 Q2179.94 191.942 2182 190.784 Q2184.08 189.627 2186.79 189.627 Q2191.26 189.627 2193.55 192.405 Q2195.84 195.159 2195.84 200.529 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2226.51 202.15 L2226.51 204.233 L2206.93 204.233 Q2207.2 208.631 2209.57 210.946 Q2211.95 213.238 2216.19 213.238 Q2218.64 213.238 2220.93 212.636 Q2223.25 212.034 2225.51 210.83 L2225.51 214.858 Q2223.22 215.83 2220.82 216.34 Q2218.41 216.849 2215.93 216.849 Q2209.73 216.849 2206.09 213.238 Q2202.48 209.627 2202.48 203.469 Q2202.48 197.104 2205.91 193.377 Q2209.36 189.627 2215.19 189.627 Q2220.42 189.627 2223.45 193.006 Q2226.51 196.363 2226.51 202.15 M2222.25 200.9 Q2222.2 197.405 2220.28 195.321 Q2218.38 193.238 2215.24 193.238 Q2211.67 193.238 2209.52 195.252 Q2207.39 197.266 2207.07 200.923 L2222.25 200.9 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /><path clip-path=\"url(#clip120)\" d=\"M2250.56 194.187 L2250.56 180.159 L2254.82 180.159 L2254.82 216.178 L2250.56 216.178 L2250.56 212.289 Q2249.22 214.603 2247.16 215.738 Q2245.12 216.849 2242.25 216.849 Q2237.55 216.849 2234.59 213.099 Q2231.65 209.349 2231.65 203.238 Q2231.65 197.127 2234.59 193.377 Q2237.55 189.627 2242.25 189.627 Q2245.12 189.627 2247.16 190.761 Q2249.22 191.872 2250.56 194.187 M2236.05 203.238 Q2236.05 207.937 2237.97 210.622 Q2239.91 213.284 2243.29 213.284 Q2246.67 213.284 2248.62 210.622 Q2250.56 207.937 2250.56 203.238 Q2250.56 198.539 2248.62 195.877 Q2246.67 193.192 2243.29 193.192 Q2239.91 193.192 2237.97 195.877 Q2236.05 198.539 2236.05 203.238 Z\" fill=\"#000000\" fill-rule=\"nonzero\" fill-opacity=\"1\" /></svg>\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["using Plots\n", "plot(x -> 2x-x^3, -2, 2, label=\"truth\")\n", "scatter!(model, -2:0.1f0:2, label=\"learned\")"]}], "metadata": {"kernelspec": {"display_name": "Julia 1.11.5", "language": "julia", "name": "julia-1.11"}, "language_info": {"file_extension": ".jl", "mimetype": "application/julia", "name": "julia", "version": "1.11.5"}}, "nbformat": 4, "nbformat_minor": 5}